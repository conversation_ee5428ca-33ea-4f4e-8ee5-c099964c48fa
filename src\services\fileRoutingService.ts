/**
 * FileRoutingService
 * Unified file handling service that replaces SharedDropboxService complexity
 * Implements the standardized file routing strategy with plugin support
 */

import { BaseService, ServiceError, ServiceErrorCode } from './base'
import { contextVaultService } from './contextVaultService'
import {
  FileDestination,
  FileUploadRequest,
  FileUploadResponse,
  FileUploadOptions,
  UploadedFile,
  FileError,
  FileRoutingConfig,
  FileValidationError,
  ValidationResult,
  FileReadRequest,
  FileReadResponse,
  FileListRequest,
  FileListResponse,
  FileDeleteRequest,
  FileDeleteResponse,
  FileOperationEvent
} from '../types/fileRouting'

export class FileRoutingService extends BaseService {
  private config: FileRoutingConfig
  private operationMetrics: Map<string, any> = new Map()

  constructor() {
    super({
      name: 'FileRoutingService',
      autoInitialize: true
    })

    this.config = this.getDefaultConfig()
  }

  protected async doInitialize(): Promise<void> {
    // Wait for electronAPI to be available
    await this.waitForElectronAPI()
    
    // Initialize context vault service if not already initialized
    if (!contextVaultService.isInitialized()) {
      await contextVaultService.initialize()
    }

    this.logger.info('FileRoutingService initialized successfully', 'doInitialize')
  }

  /**
   * Main entry point for file uploads - handles all drop zones consistently
   */
  async handleFileDrop(files: File[], destination: FileDestination): Promise<FileUploadResponse> {
    return await this.executeOperationOrThrow(
      'handleFileDrop',
      async () => {
        const startTime = Date.now()

        // Validate files
        const validation = this.validateFiles(files)
        if (!validation.valid) {
          return {
            success: false,
            files: [],
            errors: validation.errors.map(err => ({
              filename: err.filename,
              message: err.message,
              code: err.error
            }))
          }
        }

        const uploadedFiles: UploadedFile[] = []
        const errors: FileError[] = []

        // Process each file
        for (const file of files) {
          try {
            const uploadedFile = await this.processFile(file, destination)
            uploadedFiles.push(uploadedFile)
          } catch (error: any) {
            errors.push({
              filename: file.name,
              message: error.message || 'Upload failed',
              code: error.code
            })
          }
        }

        // Record metrics
        this.recordOperation({
          type: 'upload',
          contextId: destination.contextId,
          fileSize: files.reduce((sum, f) => sum + f.size, 0),
          duration: Date.now() - startTime,
          error: errors.length > 0 ? `${errors.length} files failed` : undefined
        })

        return {
          success: errors.length === 0,
          files: uploadedFiles,
          errors: errors.length > 0 ? errors : undefined
        }
      },
      { fileCount: files.length, destination }
    )
  }

  /**
   * Upload files to a specific context
   */
  async uploadToContext(files: File[], contextId: string): Promise<FileUploadResponse> {
    return this.handleFileDrop(files, { contextId, autoRoute: true })
  }

  /**
   * Upload files to a specific path
   */
  async uploadToPath(files: File[], path: string): Promise<FileUploadResponse> {
    return this.handleFileDrop(files, { path, autoRoute: false })
  }

  /**
   * Read file content
   */
  async readFile(request: FileReadRequest): Promise<FileReadResponse> {
    return await this.executeOperationOrThrow(
      'readFile',
      async () => {
        const result = await window.electronAPI.vault.readFile(request.filePath)
        
        if (result.success) {
          return {
            success: true,
            content: result.content,
            metadata: {} // TODO: Extract metadata if needed
          }
        } else {
          return {
            success: false,
            error: result.error
          }
        }
      },
      { filePath: request.filePath }
    )
  }

  /**
   * List files in a directory
   */
  async listFiles(request: FileListRequest): Promise<FileListResponse> {
    return await this.executeOperationOrThrow(
      'listFiles',
      async () => {
        const result = await window.electronAPI.vault.readDirectory(request.path)
        
        if (result.success) {
          const files = result.items.map(item => ({
            name: item.name,
            path: item.path,
            size: item.size || 0,
            mimeType: this.getMimeTypeFromPath(item.path),
            isDirectory: item.isDirectory,
            modified: item.modified
          }))

          return {
            success: true,
            files,
            totalCount: files.length
          }
        } else {
          return {
            success: false,
            files: [],
            totalCount: 0,
            error: result.error
          }
        }
      },
      { path: request.path }
    )
  }

  /**
   * Delete files
   */
  async deleteFiles(request: FileDeleteRequest): Promise<FileDeleteResponse> {
    return await this.executeOperationOrThrow(
      'deleteFiles',
      async () => {
        const deletedFiles: string[] = []
        const errors: FileError[] = []

        for (const filePath of request.filePaths) {
          try {
            const result = await window.electronAPI.vault.removeFile(filePath)
            if (result.success) {
              deletedFiles.push(filePath)
            } else {
              errors.push({
                filename: filePath,
                message: result.error || 'Delete failed'
              })
            }
          } catch (error: any) {
            errors.push({
              filename: filePath,
              message: error.message || 'Delete failed'
            })
          }
        }

        return {
          success: errors.length === 0,
          deletedFiles,
          errors: errors.length > 0 ? errors : undefined
        }
      },
      { fileCount: request.filePaths.length }
    )
  }

  /**
   * Process a single file
   */
  private async processFile(file: File, destination: FileDestination): Promise<UploadedFile> {
    // Determine destination path
    const destinationPath = await this.resolveDestinationPath(file, destination)
    
    // Convert file to buffer for IPC
    const arrayBuffer = await file.arrayBuffer()
    const uint8Array = new Uint8Array(arrayBuffer)

    // Write file using IPC
    const writeResult = await window.electronAPI.vault.writeFileBuffer(destinationPath, uint8Array)
    
    if (!writeResult.success) {
      throw new ServiceError(
        ServiceErrorCode.FILE_WRITE_ERROR,
        `Failed to write file: ${writeResult.error}`,
        { serviceName: this.serviceName, operation: 'processFile', details: { destinationPath } }
      )
    }

    // Create uploaded file record
    const uploadedFile: UploadedFile = {
      id: this.generateFileId(),
      originalName: file.name,
      savedPath: destinationPath,
      size: file.size,
      mimeType: file.type || 'application/octet-stream',
      contextId: destination.contextId
    }

    // TODO: Add plugin processing for metadata, thumbnails, text extraction
    // This would be implemented in Phase 2 of the migration strategy

    return uploadedFile
  }

  /**
   * Resolve the destination path for a file
   */
  private async resolveDestinationPath(file: File, destination: FileDestination): Promise<string> {
    // If explicit path is provided, use it
    if (destination.path) {
      return destination.path
    }

    // Get vault root
    const vaultRoot = await this.getVaultRoot()
    this.logger.debug(`Vault root resolved: ${vaultRoot}`, 'resolveDestinationPath')

    if (!vaultRoot) {
      throw new ServiceError(
        ServiceErrorCode.CONFIGURATION_ERROR,
        'No vault root configured',
        { serviceName: this.serviceName, operation: 'resolveDestinationPath' }
      )
    }

    // Determine base directory
    let baseDir: string
    if (destination.contextId) {
      // Context-specific upload
      this.logger.debug(`Looking for context: ${destination.contextId}`, 'resolveDestinationPath')

      // Ensure contextVaultService is initialized and has loaded vaults
      await contextVaultService.ensureInitialized()
      const currentVaults = contextVaultService.getCurrentVaults()
      this.logger.debug(`Available vaults: ${currentVaults.length}`, 'resolveDestinationPath')

      const contextResult = contextVaultService.findContextById(destination.contextId)
      this.logger.debug(`Context result: ${contextResult ? 'found' : 'not found'}`, 'resolveDestinationPath')

      if (!contextResult) {
        // Try reloading vaults in case they're stale
        await contextVaultService.loadVaults()
        const retryResult = contextVaultService.findContextById(destination.contextId)

        if (!retryResult) {
          throw new ServiceError(
            ServiceErrorCode.VALIDATION_ERROR,
            `Context ${destination.contextId} not found`,
            { serviceName: this.serviceName, operation: 'resolveDestinationPath' }
          )
        }
        baseDir = retryResult.context.path
      } else {
        baseDir = contextResult.context.path
      }
      this.logger.debug(`Base directory: ${baseDir}`, 'resolveDestinationPath')
    } else {
      // Shared upload
      baseDir = `${vaultRoot}/shared-dropbox`
    }

    // Apply auto-routing if enabled
    if (destination.autoRoute !== false && this.config.autoRouting.enabled) {
      const folder = this.getAutoRouteFolder(file.type)
      return `${baseDir}/${folder}/${file.name}`
    }

    return `${baseDir}/${file.name}`
  }

  /**
   * Get auto-route folder based on MIME type
   */
  private getAutoRouteFolder(mimeType: string): string {
    for (const rule of this.config.autoRouting.rules) {
      if (this.matchesMimePattern(mimeType, rule.pattern)) {
        return rule.destination
      }
    }
    return 'documents' // Default fallback
  }

  /**
   * Check if MIME type matches pattern
   */
  private matchesMimePattern(mimeType: string, pattern: string): boolean {
    if (pattern.endsWith('/*')) {
      const prefix = pattern.slice(0, -2)
      return mimeType.startsWith(prefix)
    }
    return mimeType === pattern
  }

  /**
   * Validate files before upload
   */
  private validateFiles(files: File[]): ValidationResult {
    const errors: FileValidationError[] = []

    for (const file of files) {
      // Size validation
      if (file.size > this.config.maxFileSize) {
        errors.push({
          filename: file.name,
          error: 'FILE_TOO_LARGE',
          message: `File size exceeds ${this.config.maxFileSize / 1024 / 1024}MB limit`
        })
      }

      // Type validation
      if (!this.isAllowedType(file.type)) {
        errors.push({
          filename: file.name,
          error: 'UNSUPPORTED_TYPE',
          message: `File type ${file.type} is not supported`
        })
      }

      // Name validation
      if (!this.isValidFilename(file.name)) {
        errors.push({
          filename: file.name,
          error: 'INVALID_FILENAME',
          message: 'Filename contains invalid characters'
        })
      }
    }

    return {
      valid: errors.length === 0,
      errors
    }
  }

  private isAllowedType(mimeType: string): boolean {
    return this.config.allowedTypes.some(allowedType => {
      if (allowedType.endsWith('/*')) {
        const prefix = allowedType.slice(0, -2)
        return mimeType.startsWith(prefix)
      }
      return mimeType === allowedType
    })
  }

  private isValidFilename(filename: string): boolean {
    const invalidChars = /[<>:"/\\|?*]/
    return !invalidChars.test(filename) && filename.length > 0 && filename.length <= 255
  }

  private getMimeTypeFromPath(filePath: string): string {
    const ext = filePath.split('.').pop()?.toLowerCase()
    const mimeTypes: Record<string, string> = {
      'txt': 'text/plain',
      'md': 'text/markdown',
      'json': 'application/json',
      'pdf': 'application/pdf',
      'jpg': 'image/jpeg',
      'jpeg': 'image/jpeg',
      'png': 'image/png',
      'gif': 'image/gif',
      'webp': 'image/webp'
    }
    return mimeTypes[ext || ''] || 'application/octet-stream'
  }

  private generateFileId(): string {
    return `file_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
  }

  private recordOperation(event: FileOperationEvent): void {
    // Simple metrics recording - can be enhanced later
    const key = `${event.type}_${event.contextId || 'global'}`
    const existing = this.operationMetrics.get(key) || { count: 0, totalSize: 0, errors: 0 }
    
    existing.count++
    existing.totalSize += event.fileSize || 0
    if (event.error) existing.errors++
    
    this.operationMetrics.set(key, existing)
  }

  private getDefaultConfig(): FileRoutingConfig {
    return {
      maxFileSize: 100 * 1024 * 1024, // 100MB
      allowedTypes: ['image/*', 'text/*', 'application/pdf', 'application/json'],
      autoRouting: {
        enabled: true,
        rules: [
          { pattern: 'image/*', destination: 'images' },
          { pattern: 'text/*', destination: 'documents' },
          { pattern: 'application/pdf', destination: 'documents' },
          { pattern: 'application/json', destination: 'code' }
        ]
      },
      thumbnails: {
        enabled: true,
        maxSize: { width: 200, height: 200 },
        quality: 80
      },
      textExtraction: {
        enabled: true,
        maxFileSize: 10 * 1024 * 1024 // 10MB
      },
      security: {
        scanFiles: false,
        quarantineUnsafe: false
      }
    }
  }

  /**
   * Get vault root path
   */
  private async getVaultRoot(): Promise<string | null> {
    try {
      const registry = await window.electronAPI.vault.getVaultRegistry()
      return registry?.vaultRoot || null
    } catch (error) {
      this.logger.error('Failed to get vault root', 'getVaultRoot', error)
      return null
    }
  }

  /**
   * Wait for electronAPI to be available
   */
  private async waitForElectronAPI(): Promise<void> {
    let attempts = 0
    const maxAttempts = 50
    
    while (!window.electronAPI?.vault && attempts < maxAttempts) {
      await new Promise(resolve => setTimeout(resolve, 100))
      attempts++
    }
    
    if (!window.electronAPI?.vault) {
      throw new ServiceError(
        ServiceErrorCode.INITIALIZATION_ERROR,
        'ElectronAPI not available after waiting',
        { serviceName: this.serviceName, operation: 'waitForElectronAPI' }
      )
    }
  }
}

// Export singleton instance
export const fileRoutingService = new FileRoutingService()
