/**
 * HomePageCardService
 * Manages homepage card data, context information, and integration with FileRoutingService
 * Provides unified interface for context cards with drop-to-vault functionality
 */

import { BaseService, ServiceError, ServiceErrorCode } from './base'
import { vaultUIManager } from './vaultUIManager'
import { fileRoutingService } from './fileRoutingService'
import { ContextVaultCard, ContextVault, ContextFolder } from '../types'

export interface HomePageCardData {
  id: string
  name: string
  description: string
  color: string
  icon: string
  status: 'empty' | 'active' | 'growing' | 'archived'
  fileCount: number
  conversationCount: number
  lastActivity: string
  contextType: string
  readinessScore: number
  vaultName: string
  contextId: string // Exposed for visual matching
  dropZoneEnabled: boolean
  quickActions: {
    primary: { label: string; action: string }
    secondary: { label: string; action: string }
  }
  metadata?: {
    totalSize: number
    recentFiles: string[]
    suggestedActions: string[]
    aiInsights?: string
  }
}

export interface DropToVaultOptions {
  contextId: string
  files: File[]
  autoRoute?: boolean
  targetFolder?: 'documents' | 'images' | 'artifacts'
}

export class HomePageCardService extends BaseService {
  private cardCache: Map<string, HomePageCardData> = new Map()
  private lastCacheUpdate: number = 0
  private readonly CACHE_TTL = 30000 // 30 seconds

  constructor() {
    super({
      name: 'HomePageCardService',
      autoInitialize: true
    })
  }

  protected async doInitialize(): Promise<void> {
    this.logger.info('HomePageCardService initialized', 'doInitialize')
  }

  /**
   * Get all homepage cards with context information
   */
  async getHomePageCards(forceRefresh: boolean = false): Promise<HomePageCardData[]> {
    return await this.executeOperationOrThrow(
      'getHomePageCards',
      async () => {
        // Check cache validity
        const now = Date.now()
        if (!forceRefresh && (now - this.lastCacheUpdate) < this.CACHE_TTL && this.cardCache.size > 0) {
          return Array.from(this.cardCache.values())
        }

        // Load fresh data from vaultUIManager
        const vaultCards = await vaultUIManager.getVaultCards()
        const homePageCards: HomePageCardData[] = []

        for (const vaultCard of vaultCards) {
          const homePageCard = await this.convertToHomePageCard(vaultCard)
          homePageCards.push(homePageCard)
          this.cardCache.set(homePageCard.id, homePageCard)
        }

        this.lastCacheUpdate = now
        this.logger.info(`Loaded ${homePageCards.length} homepage cards`, 'getHomePageCards')

        return homePageCards
      },
      { forceRefresh }
    )
  }

  /**
   * Get specific card by context ID
   */
  async getCardByContextId(contextId: string): Promise<HomePageCardData | null> {
    return await this.executeOperationOrThrow(
      'getCardByContextId',
      async () => {
        // Check cache first
        const cachedCard = this.cardCache.get(contextId)
        if (cachedCard) {
          return cachedCard
        }

        // Load all cards and find the one we need
        const cards = await this.getHomePageCards()
        return cards.find(card => card.contextId === contextId) || null
      },
      { contextId }
    )
  }

  /**
   * Handle file drop to vault using FileRoutingService
   */
  async handleDropToVault(options: DropToVaultOptions): Promise<{ success: boolean; uploadedFiles?: any[]; error?: string }> {
    return await this.executeOperationOrThrow(
      'handleDropToVault',
      async () => {
        try {
          // Validate context exists
          const card = await this.getCardByContextId(options.contextId)
          if (!card) {
            throw new ServiceError(
              ServiceErrorCode.NOT_FOUND,
              `Context not found: ${options.contextId}`,
              { serviceName: this.serviceName, operation: 'handleDropToVault' }
            )
          }

          // Prepare destination for FileRoutingService
          const destination = {
            contextId: options.contextId,
            path: options.targetFolder ? `/${options.targetFolder}` : undefined,
            autoRoute: options.autoRoute !== false // Default to true
          }

          // Use FileRoutingService to handle the upload
          const uploadResult = await fileRoutingService.uploadToContext(options.files, options.contextId)

          if (uploadResult.success) {
            // Invalidate cache to force refresh on next load
            this.invalidateCache()

            this.logger.info(`Successfully uploaded ${options.files.length} files to context ${options.contextId}`, 'handleDropToVault', {
              contextId: options.contextId,
              fileCount: options.files.length,
              targetFolder: options.targetFolder
            })

            return {
              success: true,
              uploadedFiles: uploadResult.files
            }
          } else {
            // Handle errors from the upload result
            const errorMessage = uploadResult.errors && uploadResult.errors.length > 0
              ? uploadResult.errors.map(e => `${e.filename}: ${e.message}`).join('; ')
              : 'Upload failed'

            throw new ServiceError(
              ServiceErrorCode.OPERATION_FAILED,
              errorMessage,
              { serviceName: this.serviceName, operation: 'handleDropToVault' }
            )
          }
        } catch (error: any) {
          this.logger.error(`Drop to vault failed: ${error.message}`, 'handleDropToVault', {
            contextId: options.contextId,
            fileCount: options.files.length,
            error: error.message
          })

          return {
            success: false,
            error: error.message || 'Unknown error occurred'
          }
        }
      },
      { contextId: options.contextId, fileCount: options.files.length }
    )
  }

  /**
   * Refresh card data for specific context
   */
  async refreshCard(contextId: string): Promise<HomePageCardData | null> {
    return await this.executeOperationOrThrow(
      'refreshCard',
      async () => {
        // Remove from cache
        this.cardCache.delete(contextId)
        
        // Reload card data
        return await this.getCardByContextId(contextId)
      },
      { contextId }
    )
  }

  /**
   * Get context summary for AI insights
   */
  async getContextSummary(contextId: string): Promise<string> {
    return await this.executeOperationOrThrow(
      'getContextSummary',
      async () => {
        const card = await this.getCardByContextId(contextId)
        if (!card) {
          return 'Context intelligence will be showing here once you\'ve started working on it.'
        }

        if (card.fileCount === 0 && card.conversationCount === 0) {
          return 'Context intelligence will be showing here once you\'ve started working on it.'
        }

        // Generate basic summary based on available data
        const parts = []
        if (card.fileCount > 0) {
          parts.push(`${card.fileCount} files`)
        }
        if (card.conversationCount > 0) {
          parts.push(`${card.conversationCount} conversations`)
        }

        const summary = `This ${card.contextType} context contains ${parts.join(' and ')}.`
        return summary + (card.metadata?.aiInsights ? ` ${card.metadata.aiInsights}` : '')
      },
      { contextId }
    )
  }

  /**
   * Convert vault card to homepage card format
   */
  private async convertToHomePageCard(vaultCard: ContextVaultCard): Promise<HomePageCardData> {
    return {
      id: vaultCard.id,
      name: vaultCard.name,
      description: vaultCard.description,
      color: vaultCard.color,
      icon: vaultCard.icon,
      status: vaultCard.status,
      fileCount: vaultCard.fileCount,
      conversationCount: vaultCard.conversationCount,
      lastActivity: vaultCard.lastActivity,
      contextType: vaultCard.contextType,
      readinessScore: vaultCard.readinessScore,
      vaultName: vaultCard.vaultName || 'Unknown Vault',
      contextId: vaultCard.id, // Expose context ID for visual matching
      dropZoneEnabled: true,
      quickActions: vaultCard.quickActions || {
        primary: { label: 'Drop Files', action: 'drop' },
        secondary: { label: 'Open Context', action: 'open' }
      },
      metadata: {
        totalSize: 0, // TODO: Calculate from file stats
        recentFiles: vaultCard.recentFiles || [],
        suggestedActions: vaultCard.suggestedActions || [],
        aiInsights: undefined // TODO: Generate AI insights
      }
    }
  }

  /**
   * Invalidate cache to force refresh
   */
  private invalidateCache(): void {
    this.cardCache.clear()
    this.lastCacheUpdate = 0
  }
}

// Export singleton instance
export const homePageCardService = new HomePageCardService()
